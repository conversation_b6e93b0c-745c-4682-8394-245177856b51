#!/usr/bin/env python3
"""
ksmbd 测试环境设置脚本

这个脚本帮助设置和验证 ksmbd 测试环境
"""

import subprocess
import sys
import os

def check_dependencies():
    """检查必要的依赖"""
    print("[*] 检查依赖...")
    
    try:
        import impacket
        print("[+] impacket 已安装")
    except ImportError:
        print("[!] impacket 未安装，请运行: pip install impacket")
        return False
    
    return True

def check_ksmbd_service():
    """检查 ksmbd 服务状态"""
    print("[*] 检查 ksmbd 服务...")
    
    try:
        # 检查 ksmbd 模块是否加载
        result = subprocess.run(['lsmod'], capture_output=True, text=True)
        if 'ksmbd' in result.stdout:
            print("[+] ksmbd 内核模块已加载")
        else:
            print("[!] ksmbd 内核模块未加载")
            print("    请运行: sudo modprobe ksmbd")
            
        # 检查 ksmbd 服务状态
        result = subprocess.run(['systemctl', 'status', 'ksmbd'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("[+] ksmbd 服务正在运行")
        else:
            print("[!] ksmbd 服务未运行")
            print("    请运行: sudo systemctl start ksmbd")
            
    except Exception as e:
        print(f"[!] 检查 ksmbd 服务时出错: {e}")

def create_test_config():
    """创建测试配置文件"""
    print("[*] 创建测试配置...")
    
    # ksmbd 配置文件内容
    ksmbd_conf = """[global]
    netbios name = KSMBD-TEST
    workgroup = WORKGROUP
    server string = ksmbd test server
    server min protocol = SMB2_10
    server max protocol = SMB3_11
    
[example]
    path = /tmp/ksmbd-test
    valid users = example
    read only = no
    browseable = yes
    writeable = yes
    guest ok = no
"""
    
    # 用户配置
    user_conf = """example:1000:XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX:XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX:[U          ]:LCT-00000001:
"""
    
    print("请手动创建以下配置:")
    print("\n1. 创建 /etc/ksmbd/ksmbd.conf:")
    print(ksmbd_conf)
    print("\n2. 创建 /etc/ksmbd/ksmbdpwd.db:")
    print("   sudo ksmbd.adduser -a example")
    print("\n3. 创建共享目录:")
    print("   sudo mkdir -p /tmp/ksmbd-test")
    print("   sudo chown example:example /tmp/ksmbd-test")
    print("   sudo chmod 755 /tmp/ksmbd-test")

def test_connection(target_ip="127.0.0.1"):
    """测试 SMB 连接"""
    print(f"[*] 测试连接到 {target_ip}...")
    
    try:
        from impacket import smb3
        
        conn = smb3.SMB3(target_ip, target_ip, sess_port=445, timeout=5)
        print("[+] 成功连接到 SMB 服务器")
        
        # 尝试登录
        try:
            conn.login("example", "example")
            print("[+] 登录成功")
            
            # 尝试连接共享
            try:
                tid = conn.connectTree("example")
                print("[+] 成功连接到共享 'example'")
                conn.disconnectTree(tid)
            except Exception as e:
                print(f"[!] 连接共享失败: {e}")
                
            conn.logoff()
        except Exception as e:
            print(f"[!] 登录失败: {e}")
            
        conn.close()
        
    except Exception as e:
        print(f"[!] 连接失败: {e}")

def main():
    print("=" * 60)
    print("ksmbd 测试环境设置")
    print("=" * 60)
    
    if not check_dependencies():
        sys.exit(1)
    
    check_ksmbd_service()
    create_test_config()
    
    print("\n" + "=" * 60)
    print("设置完成后，运行以下命令测试连接:")
    print("python3 setup_test_env.py test")
    print("=" * 60)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_connection()
    else:
        main()
