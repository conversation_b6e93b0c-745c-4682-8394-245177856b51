#!/usr/bin/env python3
"""
POC 测试脚本 - 验证代码基本功能
"""

import sys
import importlib.util

def test_imports():
    """测试导入依赖"""
    print("[*] 测试导入依赖...")
    
    try:
        from impacket import smb3
        print("[+] impacket.smb3 导入成功")
    except ImportError as e:
        print(f"[!] impacket 导入失败: {e}")
        print("    请运行: pip install impacket")
        return False
    
    try:
        import threading
        import time
        print("[+] 标准库导入成功")
    except ImportError as e:
        print(f"[!] 标准库导入失败: {e}")
        return False
    
    return True

def test_poc_syntax():
    """测试 POC 代码语法"""
    print("[*] 测试 POC 代码语法...")
    
    try:
        spec = importlib.util.spec_from_file_location("poc", "poc.py")
        poc_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(poc_module)
        print("[+] POC 代码语法正确")
        return True
    except Exception as e:
        print(f"[!] POC 代码语法错误: {e}")
        return False

def test_functions():
    """测试函数定义"""
    print("[*] 测试函数定义...")
    
    try:
        import poc
        
        # 检查必要的函数是否存在
        required_functions = [
            'smb_login',
            'send_continuous_requests', 
            'send_logoff',
            'create_session_binding',
            'main'
        ]
        
        for func_name in required_functions:
            if hasattr(poc, func_name):
                print(f"[+] 函数 {func_name} 存在")
            else:
                print(f"[!] 函数 {func_name} 不存在")
                return False
        
        return True
    except Exception as e:
        print(f"[!] 测试函数定义失败: {e}")
        return False

def main():
    print("=" * 50)
    print("POC 代码测试")
    print("=" * 50)
    
    tests = [
        ("导入依赖", test_imports),
        ("代码语法", test_poc_syntax),
        ("函数定义", test_functions)
    ]
    
    all_passed = True
    
    for test_name, test_func in tests:
        print(f"\n--- 测试: {test_name} ---")
        if not test_func():
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("[+] 所有测试通过！POC 代码准备就绪")
        print("\n使用方法:")
        print("1. 修改 poc.py 中的目标参数")
        print("2. 运行: python3 poc.py")
    else:
        print("[!] 部分测试失败，请检查代码")
    print("=" * 50)

if __name__ == "__main__":
    main()
