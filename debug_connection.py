#!/usr/bin/env python3
"""
调试连接问题的简化脚本
"""

from impacket import smb3
import traceback

def test_basic_connection(target_ip, username, password):
    """测试基本连接"""
    print(f"[*] 测试连接到 {target_ip}...")
    print(f"[*] 用户名: {username}")
    print(f"[*] 密码: {password}")

    try:
        # 创建连接
        print("[*] 创建 SMB3 连接...")
        conn = smb3.SMB3(target_ip, target_ip, sess_port=445, timeout=10)
        print("[+] SMB3 连接创建成功")

        # 尝试登录
        print("[*] 尝试登录...")
        conn.login(username, password)
        print("[+] 登录成功")

        # 获取会话信息
        print("[*] 获取会话信息...")
        try:
            session_key = conn.getSessionKey()
            print(f"[+] 会话密钥类型: {type(session_key)}")
            print(f"[+] 会话密钥长度: {len(session_key) if hasattr(session_key, '__len__') else 'N/A'}")

            if isinstance(session_key, bytes):
                print(f"[+] 会话密钥 (hex): {session_key.hex()}")
            elif isinstance(session_key, int):
                print(f"[+] 会话密钥 (int): {session_key}")
            else:
                print(f"[+] 会话密钥: {session_key}")

        except Exception as e:
            print(f"[!] 获取会话密钥失败: {e}")

        # 测试连接到一个共享
        print("[*] 测试连接到共享...")
        try:
            # 尝试连接到 IPC$ 共享（通常总是存在）
            tid = conn.connectTree('IPC$')
            print("[+] 成功连接到 IPC$ 共享")
            conn.disconnectTree(tid)
            print("[+] 成功断开 IPC$ 共享")
        except Exception as e:
            print(f"[!] 连接共享失败: {e}")

        # 测试登出
        print("[*] 测试登出...")
        try:
            conn.logoff()
            print("[+] 成功登出")
        except Exception as e:
            print(f"[!] 登出失败: {e}")

        # 关闭连接
        print("[*] 关闭连接...")
        try:
            conn.getSMBServer().get_socket().close()
            print("[+] 连接已关闭")
        except Exception as e:
            print(f"[!] 关闭连接失败: {e}")

        return True

    except Exception as e:
        print(f"[!] 连接测试失败: {e}")
        print("[!] 详细错误信息:")
        traceback.print_exc()
        return False

def test_multiple_connections(target_ip, username, password):
    """测试多个连接"""
    print("\n" + "="*50)
    print("测试多个连接")
    print("="*50)

    connections = []

    try:
        # 创建多个连接
        for i in range(2):
            print(f"[*] 创建第 {i+1} 个连接...")

            conn = smb3.SMB3(target_ip, target_ip, sess_port=445, timeout=10)
            conn.login(username, password)
            connections.append(conn)

            print(f"[+] 第 {i+1} 个连接创建成功")

            # 获取会话信息
            try:
                session_key = conn.getSessionKey()
                if isinstance(session_key, bytes):
                    session_id = session_key.hex()
                else:
                    session_id = str(session_key)
                print(f"[+] 连接 {i+1} 会话ID: {session_id}")
            except Exception as e:
                print(f"[!] 连接 {i+1} 获取会话ID失败: {e}")

        print(f"[+] 成功创建 {len(connections)} 个连接")

        # 测试并发操作
        print("[*] 测试并发操作...")
        for i, conn in enumerate(connections):
            try:
                tid = conn.connectTree('IPC$')
                conn.disconnectTree(tid)
                print(f"[+] 连接 {i+1} 可以正常操作")
            except Exception as e:
                print(f"[!] 连接 {i+1} 操作失败: {e}")

        return True

    except Exception as e:
        print(f"[!] 多连接测试失败: {e}")
        traceback.print_exc()
        return False

    finally:
        # 清理连接
        for i, conn in enumerate(connections):
            try:
                conn.logoff()
                conn.getSMBServer().get_socket().close()
                print(f"[+] 连接 {i+1} 已关闭")
            except:
                pass

def main():
    # 配置参数
    target_ip = "**************"
    username = "smb"
    password = "smb"

    print("SMB 连接调试工具")
    print("="*50)
    print(f"目标: {target_ip}")
    print(f"用户: {username}")
    print(f"密码: {password}")
    print("="*50)

    # 测试基本连接
    if test_basic_connection(target_ip, username, password):
        print("\n[+] 基本连接测试通过")

        # 测试多个连接
        if test_multiple_connections(target_ip, username, password):
            print("\n[+] 多连接测试通过")
            print("\n[*] 连接测试完成，可以运行 POC")
        else:
            print("\n[!] 多连接测试失败")
    else:
        print("\n[!] 基本连接测试失败")
        print("\n建议检查:")
        print("1. 目标 IP 是否正确")
        print("2. ksmbd 服务是否运行")
        print("3. 用户名和密码是否正确")
        print("4. 防火墙设置")

if __name__ == "__main__":
    main()
