#!/usr/bin/env python3
"""
清理测试文件脚本
删除 POC 测试过程中创建的文件
"""

from impacket import smb3
import re

def cleanup_test_files(target_ip, username, password, share_name):
    """清理测试文件"""
    print(f"[*] 连接到 {target_ip} 清理测试文件...")

    try:
        # 建立连接
        conn = smb3.SMB3(target_ip, target_ip, sess_port=445, timeout=10)
        conn.login(username, password)
        print(f"[+] 登录成功")

        # 连接到共享
        tid = conn.connectTree(share_name)
        print(f"[+] 连接到共享 '{share_name}' 成功")

        # 列出文件
        print(f"[*] 查找测试文件...")
        try:
            # 查找匹配的测试文件
            files = conn.listPath(share_name, '*')
            test_files = []

            for file_info in files:
                filename = file_info.get_longname()
                # 匹配测试文件模式
                if (filename.startswith('test_file_') or
                    filename.startswith('uaf_test_') or
                    filename.startswith('ksmbd_uaf_test_')):
                    test_files.append(filename)

            if test_files:
                print(f"[+] 找到 {len(test_files)} 个测试文件")

                # 删除测试文件
                deleted_count = 0
                for filename in test_files:
                    try:
                        # 使用正确的删除方法
                        conn.remove(share_name, filename)
                        print(f"[+] 删除文件: {filename}")
                        deleted_count += 1
                    except Exception as e:
                        print(f"[!] 删除文件 {filename} 失败: {e}")

                print(f"[+] 成功删除 {deleted_count} 个测试文件")
            else:
                print(f"[*] 没有找到测试文件")

        except Exception as e:
            print(f"[!] 列出文件失败: {e}")

        # 断开连接
        conn.disconnectTree(tid)
        conn.logoff()
        print(f"[+] 清理完成")

    except Exception as e:
        print(f"[!] 清理失败: {e}")

def main():
    # 配置参数
    target_ip = "**************"
    username = "smb"
    password = "smb"
    share_name = "example"

    print("=" * 50)
    print("ksmbd 测试文件清理工具")
    print("=" * 50)
    print(f"目标: {target_ip}")
    print(f"用户: {username}")
    print(f"共享: {share_name}")
    print("=" * 50)

    cleanup_test_files(target_ip, username, password, share_name)

if __name__ == "__main__":
    main()
