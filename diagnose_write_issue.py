#!/usr/bin/env python3
"""
诊断写入问题的脚本
测试不同的权限组合和写入方式
"""

from impacket import smb3

def test_write_permissions(target_ip, username, password, share_name):
    """测试不同的写入权限组合"""
    print(f"[*] 诊断写入问题...")
    
    try:
        # 建立连接
        conn = smb3.SMB3(target_ip, target_ip, sess_port=445, timeout=10)
        conn.login(username, password)
        print(f"[+] 登录成功")
        
        # 连接到共享
        tid = conn.connectTree(share_name)
        print(f"[+] 连接到共享 '{share_name}' 成功")
        
        # 测试不同的权限组合
        test_cases = [
            {
                "name": "GENERIC_WRITE",
                "access": 0x40000000,
                "description": "只写权限"
            },
            {
                "name": "GENERIC_READ | GENERIC_WRITE", 
                "access": 0x80000000 | 0x40000000,
                "description": "读写权限"
            },
            {
                "name": "FILE_WRITE_DATA",
                "access": 0x00000002,
                "description": "文件写入数据权限"
            },
            {
                "name": "FILE_APPEND_DATA",
                "access": 0x00000004,
                "description": "文件追加数据权限"
            },
            {
                "name": "FILE_WRITE_DATA | FILE_APPEND_DATA",
                "access": 0x00000002 | 0x00000004,
                "description": "写入和追加权限"
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            print(f"\n--- 测试 {i+1}: {test_case['name']} ---")
            print(f"描述: {test_case['description']}")
            print(f"权限值: 0x{test_case['access']:08X}")
            
            try:
                # 创建文件
                filename = f"write_test_{i}_{test_case['name'].lower().replace(' | ', '_').replace(' ', '_')}.txt"
                print(f"[*] 创建文件: {filename}")
                
                fid = conn.create(
                    tid, filename,
                    desiredAccess=test_case['access'],
                    shareMode=0x07,                # FILE_SHARE_READ | WRITE | DELETE
                    creationOptions=0x20,          # FILE_NON_DIRECTORY_FILE
                    creationDisposition=0x02,      # FILE_CREATE
                    fileAttributes=0x80            # FILE_ATTRIBUTE_NORMAL
                )
                print(f"[+] 文件创建成功，FID: {fid}")
                
                # 尝试写入数据
                test_data = f"TEST_DATA_{i}_PERMISSION_{test_case['name']}".encode()
                print(f"[*] 尝试写入 {len(test_data)} 字节...")
                
                bytes_written = conn.write(tid, fid, test_data, 0)
                print(f"[+] 写入结果: {bytes_written} 字节")
                
                if bytes_written > 0:
                    print(f"[+] ✅ 写入成功！")
                else:
                    print(f"[!] ❌ 写入失败 (返回 0 字节)")
                
                # 关闭文件
                conn.close(tid, fid)
                print(f"[+] 文件已关闭")
                
                # 尝试删除文件
                try:
                    conn.remove(share_name, filename)
                    print(f"[+] 文件已删除")
                except Exception as del_e:
                    print(f"[!] 删除失败: {del_e}")
                
            except Exception as e:
                print(f"[!] 测试失败: {e}")
        
        # 测试不同的创建方式
        print(f"\n--- 测试不同的创建方式 ---")
        
        creation_tests = [
            {
                "name": "FILE_CREATE",
                "disposition": 0x02,
                "description": "创建新文件"
            },
            {
                "name": "FILE_OPEN_IF", 
                "disposition": 0x05,
                "description": "创建或打开文件"
            },
            {
                "name": "FILE_OVERWRITE_IF",
                "disposition": 0x04,
                "description": "覆盖或创建文件"
            }
        ]
        
        for i, test in enumerate(creation_tests):
            print(f"\n测试创建方式: {test['name']}")
            try:
                filename = f"creation_test_{i}_{test['name'].lower()}.txt"
                
                fid = conn.create(
                    tid, filename,
                    desiredAccess=0x40000000,      # GENERIC_WRITE
                    shareMode=0x07,
                    creationOptions=0x20,
                    creationDisposition=test['disposition'],
                    fileAttributes=0x80
                )
                
                test_data = f"CREATION_TEST_{test['name']}".encode()
                bytes_written = conn.write(tid, fid, test_data, 0)
                print(f"[+] {test['name']}: 写入 {bytes_written} 字节")
                
                conn.close(tid, fid)
                
                try:
                    conn.remove(share_name, filename)
                except:
                    pass
                    
            except Exception as e:
                print(f"[!] {test['name']} 失败: {e}")
        
        # 断开连接
        conn.disconnectTree(tid)
        conn.logoff()
        print(f"\n[+] 诊断完成")
        
    except Exception as e:
        print(f"[!] 诊断失败: {e}")

def main():
    # 配置参数
    target_ip = "**************"
    username = "smb"
    password = "smb"
    share_name = "example"
    
    print("=" * 60)
    print("ksmbd 写入问题诊断工具")
    print("=" * 60)
    print(f"目标: {target_ip}")
    print(f"用户: {username}")
    print(f"共享: {share_name}")
    print("=" * 60)
    
    test_write_permissions(target_ip, username, password, share_name)

if __name__ == "__main__":
    main()
