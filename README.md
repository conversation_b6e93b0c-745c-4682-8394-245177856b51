# ksmbd Use-After-Free 漏洞概念验证

## 漏洞描述

这是一个针对 Linux 内核 ksmbd (内核 SMB 服务器) 中 use-after-free 漏洞的概念验证代码。

### 漏洞原理

在 ksmbd 中存在一个竞争条件漏洞：

1. **会话绑定**: 第二个传输绑定到现有会话 (SMB 3.0+, `conn->binding == true`)
2. **Worker-A**: 接收正常请求，`smb2_check_user_session()` 存储指向现有 `ksmbd_session` 的指针，并增加会话引用计数，**但不对 `sess->user` 取引用**
3. **Worker-B**: 处理 SMB2 LOGOFF，执行 `smb2_session_logoff()`，释放 `sess->user`
4. **Use-After-Free**: Worker-A 继续执行并解引用已释放的 `sess->user`

### 受影响的代码路径

```c
// smb2_session_logoff() 中的相关部分
if (sess->user) {
    ksmbd_free_user(sess->user);   /* (1) 释放内存 */
    sess->user = NULL;             /* (2) 清空字段 */
}
```

Worker-B 不会等待其他仍在使用会话的连接，只等待自己连接上的运行请求。

## 文件说明

- `poc.py`: 主要的概念验证代码
- `setup_test_env.py`: 测试环境设置脚本
- `README.md`: 本说明文档

## 环境要求

### 软件依赖

```bash
pip install impacket
```

### 目标系统要求

- 运行 ksmbd 的 Linux 系统
- 启用 SMB 3.0+ 支持
- 配置了有效的用户和共享

## 使用方法

### 1. 设置测试环境

```bash
# 检查依赖和环境
python3 setup_test_env.py

# 按照提示配置 ksmbd
```

### 2. 配置目标参数

编辑 `poc.py` 中的以下参数：

```python
target_ip = "**************"  # 目标 IP 地址
username = "example"          # 有效的用户名
password = "example"          # 对应的密码
share_name = "example"        # 存在的共享目录名
```

### 3. 运行概念验证

```bash
python3 poc.py
```

## 攻击流程

1. **建立会话绑定**: 创建两个 SMB 连接，模拟会话绑定场景
2. **启动 Worker-A**: 在第二个连接上持续发送需要 `sess->user` 验证的请求
3. **启动 Worker-B**: 在第一个连接上发送 SMB2_LOGOFF，触发 `sess->user` 释放
4. **竞争条件**: Worker-A 可能在 Worker-B 释放 `sess->user` 后继续访问它

## 预期结果

如果漏洞存在且被成功触发：

- 内核可能发生崩溃 (kernel panic)
- ksmbd 服务可能停止响应
- 系统日志中可能出现内存访问错误

## 注意事项

⚠️ **警告**: 此代码仅用于安全研究和漏洞验证目的。

- 只在授权的测试环境中使用
- 可能导致目标系统不稳定或崩溃
- 不要在生产环境中运行

## 防护措施

- 更新到最新版本的内核
- 应用相关的安全补丁
- 在 `sess->user` 访问时添加适当的同步机制

## 技术细节

### 关键函数

- `smb2_check_user_session()`: 检查用户会话，存储 `sess->user` 指针
- `smb2_session_logoff()`: 处理登出，释放 `sess->user`
- `ksmbd_free_user()`: 实际释放用户结构的内存

### 竞争窗口

竞争条件发生在以下时间窗口：
1. Worker-A 获取 `sess->user` 指针
2. Worker-B 释放 `sess->user` 内存
3. Worker-A 尝试访问已释放的 `sess->user`

## 参考资料

- Linux 内核 ksmbd 源代码
- SMB 3.0+ 协议规范
- 内存安全和竞争条件相关文档
